# Hướng dẫn cài đặt SQL Server trên macOS

Hướng dẫn này sẽ giúp bạn cài đặt và chạy SQL Server trên macOS bằng cách sử dụng Homebrew và Docker.

## Yêu cầu hệ thống

- macOS 10.14 trở lên
- Ít nhất 4GB RAM
- Khoảng 2GB dung lượng ổ cứng trống

## Cài đặt tự động

### Cách 1: Chạy script tự động

```bash
chmod +x setup-sqlserver.sh
./setup-sqlserver.sh
```

Script sẽ tự động thực hiện tất cả các bước cài đặt bên dưới.

## Cài đặt thủ công

### Bước 1: Cài đặt Homebrew

Nếu chưa có Homebrew, cài đặt bằng lệnh:

```bash
/bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"
```

### Bước 2: <PERSON><PERSON><PERSON> đặt Docker Desktop

```bash
brew install --cask docker
```

<PERSON>u khi cài đặt, mở Docker Desktop từ Applications để khởi động Docker daemon.

### Bước 3: Cài đặt Azure Data Studio (Tùy chọn)

```bash
brew install --cask azure-data-studio
```

Azure Data Studio là công cụ GUI để quản lý SQL Server.

### Bước 4: Tải SQL Server Docker Image

```bash
docker pull mcr.microsoft.com/mssql/server:2022-latest
```

### Bước 5: Chạy SQL Server Container

```bash
docker run -e "ACCEPT_EULA=Y" -e "MSSQL_SA_PASSWORD=YourStrong@Passw0rd" \
   -p 1433:1433 --name sqlserver \
   -d mcr.microsoft.com/mssql/server:2022-latest
```

## Thông tin kết nối

Sau khi cài đặt thành công:

- **Server**: `localhost,1433` hoặc `127.0.0.1,1433`
- **Username**: `sa`
- **Password**: `YourStrong@Passw0rd`
- **Port**: `1433`

## Kết nối với SQL Server

### Sử dụng Azure Data Studio

1. Mở Azure Data Studio
2. Click "New Connection"
3. Nhập thông tin:
   - Server: `localhost,1433`
   - Authentication type: SQL Login
   - User name: `sa`
   - Password: `YourStrong@Passw0rd`
4. Click "Connect"

### Sử dụng sqlcmd (Command Line)

Cài đặt sqlcmd:
```bash
brew install sqlcmd
```

Kết nối:
```bash
sqlcmd -S localhost,1433 -U sa -P 'YourStrong@Passw0rd'
```

## Quản lý Container

### Kiểm tra trạng thái container

```bash
docker ps
```

### Dừng SQL Server

```bash
docker stop sqlserver
```

### Khởi động lại SQL Server

```bash
docker start sqlserver
```

### Xóa container

```bash
docker rm -f sqlserver
```

## Thay đổi mật khẩu

Để thay đổi mật khẩu SA, tạo container mới với mật khẩu khác:

```bash
docker rm -f sqlserver
docker run -e "ACCEPT_EULA=Y" -e "MSSQL_SA_PASSWORD=MatKhauMoi123!" \
   -p 1433:1433 --name sqlserver \
   -d mcr.microsoft.com/mssql/server:2022-latest
```

**Lưu ý**: Mật khẩu phải có ít nhất 8 ký tự, bao gồm chữ hoa, chữ thường, số và ký tự đặc biệt.

## Khắc phục sự cố

### Docker không khởi động được

1. Mở Docker Desktop từ Applications
2. Đợi cho đến khi Docker hoàn toàn khởi động
3. Thử lại lệnh docker

### Container không chạy được

Kiểm tra logs:
```bash
docker logs sqlserver
```

### Không kết nối được

1. Kiểm tra container đang chạy: `docker ps`
2. Kiểm tra port 1433 có bị chiếm dụng: `lsof -i :1433`
3. Thử restart container: `docker restart sqlserver`

## Gỡ cài đặt

Để gỡ bỏ hoàn toàn:

```bash
# Xóa container
docker rm -f sqlserver

# Xóa image
docker rmi mcr.microsoft.com/mssql/server:2022-latest

# Gỡ Azure Data Studio (tùy chọn)
brew uninstall --cask azure-data-studio

# Gỡ Docker Desktop (tùy chọn)
brew uninstall --cask docker
```

## Tài liệu tham khảo

- [SQL Server on Docker](https://docs.microsoft.com/en-us/sql/linux/quickstart-install-connect-docker)
- [Azure Data Studio](https://docs.microsoft.com/en-us/sql/azure-data-studio/)
- [Docker Desktop for Mac](https://docs.docker.com/desktop/mac/)

---

**Lưu ý bảo mật**: Đây là cấu hình cho môi trường phát triển. Trong môi trường production, hãy sử dụng mật khẩu mạnh và cấu hình bảo mật phù hợp.